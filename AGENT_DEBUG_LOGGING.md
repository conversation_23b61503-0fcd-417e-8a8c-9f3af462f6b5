# Agent Debug Logging

This document describes the comprehensive logging added to help debug why agents are ending early.

## Logging Categories

### 1. Iteration Tracking
- **Location**: `src/main/llm.ts` - Start of each iteration
- **Purpose**: Track when each iteration begins and basic state
- **Log Format**: `[Agent Debug] ===== STARTING ITERATION X/Y =====`
- **Information Logged**:
  - Current iteration number
  - Max iterations
  - Conversation history length
  - Progress steps count

### 2. LLM Call Logging
- **Location**: `src/main/llm.ts` - Before and after LLM calls
- **Purpose**: Track LLM request/response details
- **Log Format**: `[Agent Debug] Iteration X - Making LLM call...`
- **Information Logged**:
  - Available tools count
  - Conversation history entries
  - Messages being sent
  - Response structure (content, toolCalls, needsMoreWork)

### 3. JSON Extraction Logging
- **Location**: `src/main/llm-fetch.ts` - JSON parsing process
- **Purpose**: Debug structured output parsing issues
- **Log Format**: `[JSON Debug]` and `[LLM Debug]`
- **Information Logged**:
  - Raw response content length and preview
  - JSON regex matching success/failure
  - Parsed JSON structure details
  - needsMoreWork value extraction

### 4. Completion Detection Logging
- **Location**: `src/main/llm.ts` - Multiple completion check points
- **Purpose**: Track why agents decide to complete
- **Log Formats**: 
  - `[Agent Debug] Iteration X - Completion Check:`
  - `[Agent Debug] Iteration X - EARLY COMPLETION DETECTED`
- **Information Logged**:
  - hasToolCalls status
  - toolCalls array length
  - needsMoreWork value
  - isComplete decision reasoning

### 5. Tool Execution Logging
- **Location**: `src/main/llm.ts` - After tool execution
- **Purpose**: Track tool success/failure impact on completion
- **Log Format**: `[Agent Debug] Iteration X - Tool Execution Summary:`
- **Information Logged**:
  - Total tools executed
  - Successful vs failed tool counts
  - All tools successful status
  - Failed tool names

### 6. Post-Tool Completion Logging
- **Location**: `src/main/llm.ts` - After tool execution completion check
- **Purpose**: Track completion decisions after tool execution
- **Log Format**: `[Agent Debug] Iteration X - Post-Tool Completion Check:`
- **Information Logged**:
  - agentIndicatedDone status
  - allToolsSuccessful status
  - needsMoreWork value
  - Failed tools list

### 7. Continuation Decision Logging
- **Location**: `src/main/llm.ts` - Loop continuation logic
- **Purpose**: Track why agents continue or stop iterating
- **Log Format**: `[Agent Debug] Iteration X - Continuation Check:`
- **Information Logged**:
  - shouldContinue decision
  - needsMoreWork value
  - Whether continuing to next iteration

### 8. Max Iterations Logging
- **Location**: `src/main/llm.ts` - When max iterations reached
- **Purpose**: Track timeout scenarios
- **Log Format**: `[Agent Debug] ===== MAX ITERATIONS REACHED =====`
- **Information Logged**:
  - Final iteration count
  - Max iterations limit
  - Final content length
  - Recent error analysis

### 9. Final Summary Logging
- **Location**: `src/main/llm.ts` - End of agent execution
- **Purpose**: Provide execution summary
- **Log Format**: `[Agent Debug] ===== AGENT EXECUTION COMPLETED =====`
- **Information Logged**:
  - Total iterations used
  - Final content length
  - Conversation history length
  - Progress steps count
  - Completion reason (MAX_ITERATIONS vs EARLY_COMPLETION)

## Key Completion Conditions to Monitor

1. **Early Completion (No Tools)**: `isComplete = true` when no tool calls and needsMoreWork is false
2. **Post-Tool Completion**: `agentIndicatedDone = true` when needsMoreWork is false after successful tool execution
3. **Fallback Completion**: `shouldContinue = false` when needsMoreWork is explicitly false
4. **Max Iterations**: Loop terminates when iteration count reaches maxIterations

## How to Use This Logging

1. **Enable Console Logging**: Ensure console output is visible in your development environment
2. **Look for Early Termination**: Search for "EARLY COMPLETION DETECTED" or "COMPLETION AFTER TOOLS"
3. **Check needsMoreWork Values**: Monitor when and why needsMoreWork becomes false
4. **Analyze Tool Impact**: Review tool execution summaries to see if tool failures affect completion
5. **Review JSON Parsing**: Check if structured output parsing is working correctly

## Common Issues to Look For

1. **Premature needsMoreWork=false**: LLM incorrectly setting needsMoreWork to false too early
2. **JSON Parsing Failures**: Structured output not being parsed correctly
3. **Tool Failure Impact**: Failed tools causing unexpected completion behavior
4. **Missing Tool Calls**: LLM not generating expected tool calls
